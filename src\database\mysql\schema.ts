import {
  mysqlTable,
  mysqlEnum,
  varchar,
  int,
  timestamp,
  bigint,
  boolean,
  char,
  text,
  index,
} from "drizzle-orm/mysql-core";

export const userRoleEnum = mysqlEnum("user_role", [
  "user",
  "admin",
  "super_admin",
]);

export const users = mysqlTable("users", {
  id: varchar("id", { length: 255 }).primaryKey(),
  name: varchar("name", { length: 255 }).notNull(),
  email: varchar("email", { length: 255 }).notNull().unique(),
  emailVerified: boolean("emailVerified").notNull(),
  image: text("image"),
  role: userRoleEnum("role").notNull().default("user"),
  paymentProviderCustomerId: varchar("paymentProviderCustomerId", { length: 255 }).unique(),
  createdAt: timestamp("createdAt").notNull(),
  updatedAt: timestamp("updatedAt").notNull(),
});

export const sessions = mysqlTable("sessions", {
  id: varchar("id", { length: 255 }).primaryKey(),
  expiresAt: timestamp("expiresAt").notNull(),
  token: varchar("token", { length: 255 }).notNull().unique(),
  createdAt: timestamp("createdAt").notNull(),
  updatedAt: timestamp("updatedAt").notNull(),
  ipAddress: varchar("ipAddress", { length: 45 }),
  userAgent: text("userAgent"),
  os: varchar("os", { length: 100 }),
  browser: varchar("browser", { length: 100 }),
  deviceType: varchar("deviceType", { length: 50 }),
  userId: varchar("userId", { length: 255 })
    .notNull()
    .references(() => users.id, { onDelete: "cascade" }),
});

export const accounts = mysqlTable(
  "accounts",
  {
    id: varchar("id", { length: 255 }).primaryKey(),
    accountId: varchar("accountId", { length: 255 }).notNull(),
    providerId: varchar("providerId", { length: 255 }).notNull(),
    userId: varchar("userId", { length: 255 })
      .notNull()
      .references(() => users.id, { onDelete: "cascade" }),
    accessToken: text("accessToken"),
    refreshToken: text("refreshToken"),
    idToken: text("idToken"),
    accessTokenExpiresAt: timestamp("accessTokenExpiresAt"),
    refreshTokenExpiresAt: timestamp("refreshTokenExpiresAt"),
    scope: text("scope"),
    password: varchar("password", { length: 255 }),
    createdAt: timestamp("createdAt").notNull(),
    updatedAt: timestamp("updatedAt").notNull(),
  },
  (table) => {
    return {
      userIdx: index("accounts_userId_idx").on(table.userId),
    };
  }
);

export const verifications = mysqlTable("verifications", {
  id: varchar("id", { length: 255 }).primaryKey(),
  identifier: varchar("identifier", { length: 255 }).notNull(),
  value: text("value").notNull(),
  expiresAt: timestamp("expiresAt").notNull(),
  createdAt: timestamp("createdAt"),
  updatedAt: timestamp("updatedAt"),
});

export const subscriptions = mysqlTable(
  "subscriptions",
  {
    id: char("id", { length: 36 }).primaryKey().$defaultFn(() => crypto.randomUUID()),
    userId: varchar("userId", { length: 255 })
      .notNull()
      .references(() => users.id, { onDelete: "cascade" }),
    customerId: varchar("customerId", { length: 255 }).notNull(),
    subscriptionId: varchar("subscriptionId", { length: 255 }).notNull().unique(),
    productId: varchar("productId", { length: 255 }).notNull(),
    status: varchar("status", { length: 50 }).notNull(),
    currentPeriodStart: timestamp("currentPeriodStart"),
    currentPeriodEnd: timestamp("currentPeriodEnd"),
    canceledAt: timestamp("canceledAt"),
    createdAt: timestamp("createdAt").notNull().defaultNow(),
    updatedAt: timestamp("updatedAt").notNull().defaultNow(),
  },
  (table) => {
    return {
      userIdx: index("subscriptions_userId_idx").on(table.userId),
      customerIdIdx: index("subscriptions_customerId_idx").on(table.customerId),
    };
  }
);

export const payments = mysqlTable(
  "payments",
  {
    id: char("id", { length: 36 }).primaryKey().$defaultFn(() => crypto.randomUUID()),
    userId: varchar("userId", { length: 255 })
      .notNull()
      .references(() => users.id, { onDelete: "cascade" }),
    customerId: varchar("customerId", { length: 255 }).notNull(),
    subscriptionId: varchar("subscriptionId", { length: 255 }),
    productId: varchar("productId", { length: 255 }).notNull(),
    paymentId: varchar("paymentId", { length: 255 }).notNull().unique(),
    amount: int("amount").notNull(),
    currency: varchar("currency", { length: 10 }).notNull().default("usd"),
    status: varchar("status", { length: 50 }).notNull(),
    paymentType: varchar("paymentType", { length: 50 }).notNull(),
    createdAt: timestamp("createdAt").notNull().defaultNow(),
    updatedAt: timestamp("updatedAt").notNull().defaultNow(),
  },
  (table) => {
    return {
      userIdx: index("payments_userId_idx").on(table.userId),
    };
  }
);

export const webhookEvents = mysqlTable(
  "webhook_events",
  {
    id: char("id", { length: 36 }).primaryKey().$defaultFn(() => crypto.randomUUID()),
    eventId: varchar("eventId", { length: 255 }).notNull().unique(),
    eventType: varchar("eventType", { length: 100 }).notNull(),
    provider: varchar("provider", { length: 50 }).notNull().default("creem"),
    processed: boolean("processed").notNull().default(true),
    processedAt: timestamp("processedAt").notNull().defaultNow(),
    payload: text("payload"),
    createdAt: timestamp("createdAt").notNull().defaultNow(),
  },
  (table) => {
    return {
      eventIdIdx: index("webhook_events_eventId_idx").on(table.eventId),
      providerIdx: index("webhook_events_provider_idx").on(table.provider),
    };
  }
);

export const uploads = mysqlTable(
  "uploads",
  {
    id: char("id", { length: 36 }).primaryKey().$defaultFn(() => crypto.randomUUID()),
    userId: varchar("userId", { length: 255 })
      .notNull()
      .references(() => users.id, { onDelete: "cascade" }),
    fileKey: varchar("fileKey", { length: 255 }).notNull(),
    url: text("url").notNull(),
    fileName: varchar("fileName", { length: 255 }).notNull(),
    fileSize: int("fileSize").notNull(),
    contentType: varchar("contentType", { length: 100 }).notNull(),
    createdAt: timestamp("createdAt").notNull().defaultNow(),
  },
  (table) => {
    return {
      userIdx: index("uploads_userId_idx").on(table.userId),
      fileKeyIdx: index("uploads_fileKey_idx").on(table.fileKey),
    };
  }
);

export const rateLimits = mysqlTable("rate_limits", {
  id: varchar("id", { length: 255 }).primaryKey(),
  key: varchar("key", { length: 255 }).notNull().unique(),
  count: int("count").notNull(),
  lastRequest: bigint("lastRequest", { mode: "number" }).notNull(),
});

export const settings = mysqlTable("settings", {
  key: varchar("key", { length: 255 }).primaryKey().notNull(),
  value: text("value").notNull(),
  updatedAt: timestamp("updatedAt").notNull().defaultNow(),
});