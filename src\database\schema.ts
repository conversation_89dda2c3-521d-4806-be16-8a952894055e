import env from "@/env";

// Dynamically export based on the engine type
switch (env.DATABASE_ENGINE) {
  case "mysql":
    module.exports = require("./mysql/schema");
    break;
  case "postgres":
    module.exports = require("./postgres/schema");
    break;
  case "sqlite":
    module.exports = require("./sqlite/schema");
    break;
  default:
    throw new Error(
      `Unsupported DATABASE_ENGINE: ${env.DATABASE_ENGINE}. Valid values are 'postgres', 'mysql', or 'sqlite'.`
    );
}
